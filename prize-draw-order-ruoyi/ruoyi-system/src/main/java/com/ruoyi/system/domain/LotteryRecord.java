package com.ruoyi.system.domain;

import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.annotation.Excel.ColumnType;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 抽奖记录表 lottery_record
 * 
 * <AUTHOR>
 */
public class LotteryRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 记录ID */
    @Excel(name = "记录ID", cellType = ColumnType.NUMERIC)
    private Long recordId;

    /** 活动ID */
    @Excel(name = "活动ID", cellType = ColumnType.NUMERIC)
    private Long activityId;

    /** 商家ID */
    @Excel(name = "商家ID", cellType = ColumnType.NUMERIC)
    private Long merchantId;

    /** 商家编码 */
    @Excel(name = "商家编码")
    private String merchantCode;

    /** 桌台ID */
    @Excel(name = "桌台ID", cellType = ColumnType.NUMERIC)
    private Long tableId;

    /** 用户OpenID */
    @Excel(name = "用户OpenID")
    private String userOpenid;

    /** 用户昵称 */
    @Excel(name = "用户昵称")
    private String userNickname;

    /** 用户头像 */
    private String userAvatar;

    /** 奖品名称 */
    @Excel(name = "奖品名称")
    private String prizeName;

    /** 奖品类型 */
    @Excel(name = "奖品类型")
    private String prizeType;

    /** 奖品价值 */
    @Excel(name = "奖品价值")
    private String prizeValue;

    /** 是否中奖（0未中奖 1中奖） */
    @Excel(name = "是否中奖", readConverterExp = "0=未中奖,1=中奖")
    private String isWinner;

    /** 领取状态（0未领取 1已领取） */
    @Excel(name = "领取状态", readConverterExp = "0=未领取,1=已领取")
    private String claimStatus;

    /** 领取时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "领取时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date claimTime;

    /** 抽奖时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "抽奖时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date drawTime;

    /** 抽奖IP */
    @Excel(name = "抽奖IP")
    private String drawIp;

    /** 活动名称（关联查询用） */
    @Excel(name = "活动名称")
    private String activityName;

    /** 商家名称（关联查询用） */
    @Excel(name = "商家名称")
    private String merchantName;

    /** 桌台号（关联查询用） */
    @Excel(name = "桌台号")
    private String tableNumber;

    public Long getRecordId()
    {
        return recordId;
    }

    public void setRecordId(Long recordId)
    {
        this.recordId = recordId;
    }

    @NotNull(message = "活动ID不能为空")
    public Long getActivityId()
    {
        return activityId;
    }

    public void setActivityId(Long activityId)
    {
        this.activityId = activityId;
    }

    @NotNull(message = "商家ID不能为空")
    public Long getMerchantId()
    {
        return merchantId;
    }

    public void setMerchantId(Long merchantId)
    {
        this.merchantId = merchantId;
    }

    @Size(min = 0, max = 50, message = "商家编码长度不能超过50个字符")
    public String getMerchantCode()
    {
        return merchantCode;
    }

    public void setMerchantCode(String merchantCode)
    {
        this.merchantCode = merchantCode;
    }

    public Long getTableId()
    {
        return tableId;
    }

    public void setTableId(Long tableId)
    {
        this.tableId = tableId;
    }

    @Size(min = 0, max = 100, message = "用户OpenID长度不能超过100个字符")
    public String getUserOpenid()
    {
        return userOpenid;
    }

    public void setUserOpenid(String userOpenid)
    {
        this.userOpenid = userOpenid;
    }

    @Size(min = 0, max = 100, message = "用户昵称长度不能超过100个字符")
    public String getUserNickname()
    {
        return userNickname;
    }

    public void setUserNickname(String userNickname)
    {
        this.userNickname = userNickname;
    }

    public String getUserAvatar()
    {
        return userAvatar;
    }

    public void setUserAvatar(String userAvatar)
    {
        this.userAvatar = userAvatar;
    }

    @Size(min = 0, max = 100, message = "奖品名称长度不能超过100个字符")
    public String getPrizeName()
    {
        return prizeName;
    }

    public void setPrizeName(String prizeName)
    {
        this.prizeName = prizeName;
    }

    @Size(min = 0, max = 20, message = "奖品类型长度不能超过20个字符")
    public String getPrizeType()
    {
        return prizeType;
    }

    public void setPrizeType(String prizeType)
    {
        this.prizeType = prizeType;
    }

    @Size(min = 0, max = 100, message = "奖品价值长度不能超过100个字符")
    public String getPrizeValue()
    {
        return prizeValue;
    }

    public void setPrizeValue(String prizeValue)
    {
        this.prizeValue = prizeValue;
    }

    public String getIsWinner()
    {
        return isWinner;
    }

    public void setIsWinner(String isWinner)
    {
        this.isWinner = isWinner;
    }

    public String getClaimStatus()
    {
        return claimStatus;
    }

    public void setClaimStatus(String claimStatus)
    {
        this.claimStatus = claimStatus;
    }

    public Date getClaimTime()
    {
        return claimTime;
    }

    public void setClaimTime(Date claimTime)
    {
        this.claimTime = claimTime;
    }

    @NotNull(message = "抽奖时间不能为空")
    public Date getDrawTime()
    {
        return drawTime;
    }

    public void setDrawTime(Date drawTime)
    {
        this.drawTime = drawTime;
    }

    @Size(min = 0, max = 50, message = "抽奖IP长度不能超过50个字符")
    public String getDrawIp()
    {
        return drawIp;
    }

    public void setDrawIp(String drawIp)
    {
        this.drawIp = drawIp;
    }

    public String getActivityName()
    {
        return activityName;
    }

    public void setActivityName(String activityName)
    {
        this.activityName = activityName;
    }

    public String getMerchantName()
    {
        return merchantName;
    }

    public void setMerchantName(String merchantName)
    {
        this.merchantName = merchantName;
    }

    public String getTableNumber()
    {
        return tableNumber;
    }

    public void setTableNumber(String tableNumber)
    {
        this.tableNumber = tableNumber;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("recordId", getRecordId())
            .append("activityId", getActivityId())
            .append("merchantId", getMerchantId())
            .append("merchantCode", getMerchantCode())
            .append("tableId", getTableId())
            .append("userOpenid", getUserOpenid())
            .append("userNickname", getUserNickname())
            .append("userAvatar", getUserAvatar())
            .append("prizeName", getPrizeName())
            .append("prizeType", getPrizeType())
            .append("prizeValue", getPrizeValue())
            .append("isWinner", getIsWinner())
            .append("claimStatus", getClaimStatus())
            .append("claimTime", getClaimTime())
            .append("drawTime", getDrawTime())
            .append("drawIp", getDrawIp())
            .append("createTime", getCreateTime())
            .append("remark", getRemark())
            .append("activityName", getActivityName())
            .append("merchantName", getMerchantName())
            .append("tableNumber", getTableNumber())
            .toString();
    }
}
