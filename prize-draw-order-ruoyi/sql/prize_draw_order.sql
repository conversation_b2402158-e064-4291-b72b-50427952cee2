-- ----------------------------
-- 抽奖点餐系统数据库表结构
-- ----------------------------

-- ----------------------------
-- 1、商家表
-- ----------------------------
DROP TABLE IF EXISTS `merchant`;
CREATE TABLE `merchant` (
  `merchant_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '商家ID',
  `merchant_name` varchar(100) NOT NULL COMMENT '商家名称',
  `merchant_code` varchar(50) NOT NULL COMMENT '商家编码',
  `contact_person` varchar(50) DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `address` varchar(200) DEFAULT NULL COMMENT '商家地址',
  `expire_time` datetime DEFAULT NULL COMMENT '到期时间',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用 2过期）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`merchant_id`),
  UNIQUE KEY `uk_merchant_code` (`merchant_code`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 COMMENT='商家表';

-- ----------------------------
-- 2、商家桌台表
-- ----------------------------
DROP TABLE IF EXISTS `merchant_table`;
CREATE TABLE `merchant_table` (
  `table_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '桌台ID',
  `merchant_id` bigint(20) NOT NULL COMMENT '商家ID',
  `table_number` varchar(20) NOT NULL COMMENT '桌台号',
  `table_name` varchar(50) DEFAULT NULL COMMENT '桌台名称',
  `qr_code` varchar(500) DEFAULT NULL COMMENT '桌台二维码',
  `meituan_link` varchar(500) DEFAULT NULL COMMENT '美团点餐链接',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`table_id`),
  KEY `idx_merchant_id` (`merchant_id`),
  UNIQUE KEY `uk_merchant_table` (`merchant_id`, `table_number`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 COMMENT='商家桌台表';

-- ----------------------------
-- 3、商家配置表
-- ----------------------------
DROP TABLE IF EXISTS `merchant_config`;
CREATE TABLE `merchant_config` (
  `config_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `merchant_id` bigint(20) NOT NULL COMMENT '商家ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` text COMMENT '配置值',
  `config_type` varchar(20) DEFAULT 'text' COMMENT '配置类型（text文本 image图片 json对象）',
  `config_desc` varchar(200) DEFAULT NULL COMMENT '配置描述',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`config_id`),
  KEY `idx_merchant_id` (`merchant_id`),
  UNIQUE KEY `uk_merchant_config` (`merchant_id`, `config_key`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 COMMENT='商家配置表';

-- ----------------------------
-- 4、抽奖活动表
-- ----------------------------
DROP TABLE IF EXISTS `lottery_activity`;
CREATE TABLE `lottery_activity` (
  `activity_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '活动ID',
  `merchant_id` bigint(20) NOT NULL COMMENT '商家ID',
  `activity_name` varchar(100) NOT NULL COMMENT '活动名称',
  `activity_desc` varchar(500) DEFAULT NULL COMMENT '活动描述',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime NOT NULL COMMENT '结束时间',
  `prize_config` json DEFAULT NULL COMMENT '奖品配置（JSON格式）',
  `draw_rules` text DEFAULT NULL COMMENT '抽奖规则',
  `daily_limit` int(11) DEFAULT 1 COMMENT '每日抽奖次数限制',
  `total_limit` int(11) DEFAULT 0 COMMENT '总抽奖次数限制（0为不限制）',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `claim_instruction` text DEFAULT NULL COMMENT '领取说明',
  `wechat_qrcode` varchar(500) DEFAULT NULL COMMENT '微信二维码图片路径',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`activity_id`),
  KEY `idx_merchant_id` (`merchant_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 COMMENT='抽奖活动表';

-- ----------------------------
-- 5、抽奖记录表
-- ----------------------------
DROP TABLE IF EXISTS `lottery_record`;
CREATE TABLE `lottery_record` (
  `record_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `activity_id` bigint(20) NOT NULL COMMENT '活动ID',
  `merchant_id` bigint(20) NOT NULL COMMENT '商家ID',
  `merchant_code` varchar(50) DEFAULT NULL COMMENT '商家编码',
  `table_id` bigint(20) DEFAULT NULL COMMENT '桌台ID',
  `user_openid` varchar(100) DEFAULT NULL COMMENT '用户OpenID',
  `user_nickname` varchar(100) DEFAULT NULL COMMENT '用户昵称',
  `user_avatar` varchar(500) DEFAULT NULL COMMENT '用户头像',
  `prize_name` varchar(100) DEFAULT NULL COMMENT '奖品名称',
  `prize_type` varchar(20) DEFAULT NULL COMMENT '奖品类型',
  `prize_value` varchar(100) DEFAULT NULL COMMENT '奖品价值',
  `is_winner` char(1) DEFAULT '0' COMMENT '是否中奖（0未中奖 1中奖）',
  `claim_status` char(1) DEFAULT '0' COMMENT '领取状态（0未领取 1已领取）',
  `claim_time` datetime DEFAULT NULL COMMENT '领取时间',
  `draw_time` datetime NOT NULL COMMENT '抽奖时间',
  `draw_ip` varchar(50) DEFAULT NULL COMMENT '抽奖IP',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`record_id`),
  KEY `idx_activity_id` (`activity_id`),
  KEY `idx_merchant_id` (`merchant_id`),
  KEY `idx_user_openid` (`user_openid`),
  KEY `idx_draw_time` (`draw_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 COMMENT='抽奖记录表';

-- ----------------------------
-- 6、用户表扩展（添加商家类型用户）
-- ----------------------------
-- 为sys_user表添加商家ID字段
ALTER TABLE `sys_user` ADD COLUMN `merchant_id` bigint(20) DEFAULT NULL COMMENT '关联商家ID（商家用户专用）';
ALTER TABLE `sys_user` ADD INDEX `idx_merchant_id` (`merchant_id`);

-- 修改用户类型字段注释
ALTER TABLE `sys_user` MODIFY COLUMN `user_type` varchar(2) DEFAULT '00' COMMENT '用户类型（00系统用户 01商家用户）';

-- ----------------------------
-- 初始化商家配置数据
-- ----------------------------
-- 默认配置项
INSERT INTO `merchant_config` (`merchant_id`, `config_key`, `config_value`, `config_type`, `config_desc`, `create_by`, `create_time`) VALUES
(0, 'claim_instruction', '请到前台出示此页面领取奖品', 'text', '领取说明默认文本', 'system', NOW()),
(0, 'scan_page_bg', '/static/images/default_bg.jpg', 'image', '扫码页面背景图片', 'system', NOW()),
(0, 'lottery_bg', '/static/images/lottery_bg.jpg', 'image', '抽奖页面背景图片', 'system', NOW());

-- ----------------------------
-- 菜单权限数据
-- ----------------------------
-- 商家管理菜单
INSERT INTO `sys_menu` VALUES (2000, '商家管理', 0, 1, 'merchant', NULL, NULL, 1, 0, 'M', '0', '0', '', 'merchant', 'admin', NOW(), '', NULL, '商家管理目录');
INSERT INTO `sys_menu` VALUES (2001, '商家列表', 2000, 1, 'list', 'merchant/list', NULL, 1, 0, 'C', '0', '0', 'merchant:info:view', 'merchant', 'admin', NOW(), '', NULL, '商家列表菜单');
INSERT INTO `sys_menu` VALUES (2002, '商家查询', 2001, 1, '', '', NULL, 1, 0, 'F', '0', '0', 'merchant:info:query', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO `sys_menu` VALUES (2003, '商家新增', 2001, 2, '', '', NULL, 1, 0, 'F', '0', '0', 'merchant:info:add', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO `sys_menu` VALUES (2004, '商家修改', 2001, 3, '', '', NULL, 1, 0, 'F', '0', '0', 'merchant:info:edit', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO `sys_menu` VALUES (2005, '商家删除', 2001, 4, '', '', NULL, 1, 0, 'F', '0', '0', 'merchant:info:remove', '#', 'admin', NOW(), '', NULL, '');

-- 抽奖管理菜单
INSERT INTO `sys_menu` VALUES (2100, '抽奖管理', 0, 2, 'lottery', NULL, NULL, 1, 0, 'M', '0', '0', '', 'lottery', 'admin', NOW(), '', NULL, '抽奖管理目录');
INSERT INTO `sys_menu` VALUES (2101, '抽奖活动', 2100, 1, 'activity', 'lottery/activity', NULL, 1, 0, 'C', '0', '0', 'lottery:activity:view', 'lottery', 'admin', NOW(), '', NULL, '抽奖活动菜单');
INSERT INTO `sys_menu` VALUES (2102, '抽奖记录', 2100, 2, 'record', 'lottery/record', NULL, 1, 0, 'C', '0', '0', 'lottery:record:view', 'record', 'admin', NOW(), '', NULL, '抽奖记录菜单');

-- 商家端菜单（商家用户专用）
INSERT INTO `sys_menu` VALUES (3000, '商家中心', 0, 1, 'merchant-center', NULL, NULL, 1, 0, 'M', '0', '0', '', 'merchant-center', 'admin', NOW(), '', NULL, '商家中心目录');
INSERT INTO `sys_menu` VALUES (3001, '桌台管理', 3000, 1, 'table', 'merchant/table', NULL, 1, 0, 'C', '0', '0', 'merchant:table:view', 'table', 'admin', NOW(), '', NULL, '桌台管理菜单');
INSERT INTO `sys_menu` VALUES (3002, '页面配置', 3000, 2, 'config', 'merchant/config', NULL, 1, 0, 'C', '0', '0', 'merchant:config:view', 'config', 'admin', NOW(), '', NULL, '页面配置菜单');
INSERT INTO `sys_menu` VALUES (3003, '抽奖设置', 3000, 3, 'lottery-setting', 'merchant/lottery', NULL, 1, 0, 'C', '0', '0', 'merchant:lottery:view', 'lottery-setting', 'admin', NOW(), '', NULL, '抽奖设置菜单');
